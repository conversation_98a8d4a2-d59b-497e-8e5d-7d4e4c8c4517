import { describe, it, expect } from 'vitest';
import { logger, httpLogger } from '../logger';

describe('logger', () => {
  it('should use the correct log level from environment', () => {
    expect(logger.level).toBe(process.env.LOG_LEVEL || 'info');
  });
});

describe('httpLogger', () => {
  it('should generte request ID from x-request-id', () => {
    const req = { headers: { 'x-request-id': 'abc123' } };
    const id = (httpLogger as any).genReqId(req as any);
    expect(id).toBe('abc123');
  });

  it('should generate request ID from x-correlation-id', () => {
    const req = { headers: { 'x-correlation-id': 'xyz789' } };
    const id = httpLogger.genReqId(req as any);
    expect(id).toBe('xyz789');
  });

  it('should generate random request ID if no header', () => {
    const req = { headers: {} };
    const id = httpLogger.genReqId(req as any);
    expect(typeof id).toBe('string');
    expect(id.length).toBeGreaterThan(0);
  });

  it('should serialize request correctly', () => {
    const req = {
      method: 'GET',
      url: '/test',
      headers: {
        'user-agent': 'test-agent',
        'content-type': 'application/json',
        'x-forwarded-for': '*******'
      },
      remoteAddress: '127.0.0.1',
      remotePort: 12345
    };
    const serialized = httpLogger.serializers.req(req as any);
    expect(serialized).toMatchObject({
      method: 'GET',
      url: '/test',
      headers: {
        'user-agent': 'test-agent',
        'content-type': 'application/json',
        'x-forwarded-for': '*******'
      },
      remoteAddress: '127.0.0.1',
      remotePort: 12345
    });
  });

  it('should serialize response correctly', () => {
    const res = {
      statusCode: 200,
      getHeader: (name: string) => (name === 'content-type' ? 'application/json' : '123')
    };
    const serialized = httpLogger.serializers.res(res as any);
    expect(serialized).toMatchObject({
      statusCode: 200,
      headers: {
        'content-type': 'application/json',
        'content-length': '123'
      }
    });
  });

  it('should set log level to warn for 4xx', () => {
    expect(httpLogger.customLogLevel({}, { statusCode: 404 }, null)).toBe('warn');
  });

  it('should set log level to error for 5xx', () => {
    expect(httpLogger.customLogLevel({}, { statusCode: 500 }, null)).toBe('error');
  });

  it('should set log level to error if error is present', () => {
    expect(httpLogger.customLogLevel({}, { statusCode: 200 }, new Error())).toBe('error');
  });

  it('should set log level to info for 3xx', () => {
    expect(httpLogger.customLogLevel({}, { statusCode: 302 }, null)).toBe('info');
  });

  it('should set log level to info for other cases', () => {
    expect(httpLogger.customLogLevel({}, { statusCode: 200 }, null)).toBe('info');
  });
});