{"result": [{"scriptId": "357", "url": "file:///D:/projects/GitHub/bm-essay-tutor/apps/api/src/__tests__/helpers/__tests__/testHelpers.test.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 12324, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 12324, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 594, "endOffset": 4886, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 665, "endOffset": 994, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 754, "endOffset": 988, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1056, "endOffset": 1719, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1142, "endOffset": 1391, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1471, "endOffset": 1713, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1780, "endOffset": 2346, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1872, "endOffset": 2083, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2162, "endOffset": 2340, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2399, "endOffset": 3190, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2477, "endOffset": 3184, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3241, "endOffset": 3660, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3326, "endOffset": 3654, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3717, "endOffset": 4882, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3801, "endOffset": 4318, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4392, "endOffset": 4876, "count": 1}], "isBlockCoverage": true}]}, {"scriptId": "359", "url": "file:///D:/projects/GitHub/bm-essay-tutor/apps/api/src/__tests__/helpers/testHelpers.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 5666, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 5666, "count": 1}], "isBlockCoverage": true}, {"functionName": "createMockOpenRouterClient", "ranges": [{"startOffset": 279, "endOffset": 380, "count": 1}], "isBlockCoverage": true}, {"functionName": "get", "ranges": [{"startOffset": 495, "endOffset": 537, "count": 1}], "isBlockCoverage": true}, {"functionName": "createMockChatResponse", "ranges": [{"startOffset": 541, "endOffset": 683, "count": 2}], "isBlockCoverage": true}, {"functionName": "get", "ranges": [{"startOffset": 794, "endOffset": 832, "count": 2}], "isBlockCoverage": true}, {"functionName": "createOpenRouterError", "ranges": [{"startOffset": 836, "endOffset": 975, "count": 2}], "isBlockCoverage": true}, {"functionName": "get", "ranges": [{"startOffset": 1085, "endOffset": 1122, "count": 2}], "isBlockCoverage": true}, {"functionName": "get", "ranges": [{"startOffset": 1486, "endOffset": 1515, "count": 10}], "isBlockCoverage": true}, {"functionName": "get", "ranges": [{"startOffset": 1738, "endOffset": 1765, "count": 7}], "isBlockCoverage": true}, {"functionName": "createChatRequest", "ranges": [{"startOffset": 1769, "endOffset": 2016, "count": 2}], "isBlockCoverage": true}, {"functionName": "get", "ranges": [{"startOffset": 2122, "endOffset": 2155, "count": 2}], "isBlockCoverage": true}]}]}