import { OpenRouterClient, ChatRequest, ChatResponse } from '../clients/openRouter.js';
import dotenv from 'dotenv';

dotenv.config();

export interface MarkingServiceRequest {
  topic: string;
  essayContent: {
    introduction: string;
    mainPoints: string[];
    conclusion: string;
  };
  model?: string;
  temperature?: number;
  max_tokens?: number;
}

export interface MarkingServiceResponse {
  success: boolean;
  data?: ChatResponse;
  error?: string;
  statusCode?: number;
}

export class MarkingService {
  constructor(private openRouterClient: OpenRouterClient) {}

  private static readonly markingSystemPrompt = `You are a Bahasa Melayu teacher marking a Year 5 student's essay.
Follow the KSSR 2022 guidelines and the detailed criteria below. You are to mark it very strictly and are not generous with scoring.
For each main criterion, give:

A numerical score (see point ranges).

Specific comments (what worked, what needs improvement).

Finally, total the marks, convert them to the 20-point national scale, state the achievement band (Cemerlang, Kepujian, Baik, Memuaskan, TPM, TM), and give concise feedback.

1 — Ideas & Content (0 – 25 pts)
Relevance to the task.

Completeness: all parts of the topic addressed.

Logical elaboration with clear examples/supporting details.

2 — Structure & Organization (0 – 20 pts)
Effective introduction and conclusion.

Logical paragraphing; one idea per paragraph.

Smooth flow and coherence; format matches text type (narrative, descriptive, etc.).

Overall presentation neat and coherent.

3 — Language Use (0 – 20 pts)
Grammar (kegramatisan): correct syntax and morphology.

Sentence variety: simple, compound, complex used appropriately.

Function words & adjectives (kata tugas, kata adjektif) used correctly.

Editing & refinement: evidence of self-correction or polished phrasing.

4 — Vocabulary (0 – 15 pts)
Range and suitability of word choice.

Effective, context-appropriate adjectives/verbs.

Reward accurate use of idioms or peribahasa.

5 — Mechanics: Spelling & Punctuation (0 – 10 pts)
Correct spelling of common and topic-specific terms.

Proper use of commas, periods, question/exclamation marks.

Minor errors that do not hinder meaning incur small penalties; frequent or disruptive errors incur larger ones.

6 — Creativity & Originality (0 – 10 pts)
Original ideas or personal insights.

Interesting voice or perspective.

Avoidance of wholesale copying.

Scoring Summary
Band (20-pt scale)\tScore Range\tDescriptor
Cemerlang (Excellent)\t18 – 20\tTask fully met; language accurate & varied; presentation exemplary.
Kepujian (Merit)\t15 – 17\tMinor errors; well-developed ideas; good vocabulary & structure.
Baik (Good)\t13 – 14\tSome errors but ideas clear; adequate vocabulary; mostly correct format.
Memuaskan (Satisfactory)\t8 – 12\tMany errors; limited elaboration; inconsistent paragraphing.
TPM – Tahap Penguasaan Minimum\t3 – 7\tIdeas partly relevant; frequent grammar issues; poor organization.
TM – Tidak Mencapai TPM\t0 – 2\tIdeas irrelevant/missing; very poor language; no proper format.

Conversion rule: 20-pt score = round(Total / 5)
(e.g., 76/100 → 15/20 → Kepujian)

Output Template
📝 **Essay Marking Report**

1. Ideas & Content: __ /25  
   - Comment:

2. Structure & Organization: __ /20  
   - Comment:

3. Language Use: __ /20  
   - Comment:

4. Vocabulary: __ /15  
   - Comment:

5. Mechanics (Spelling & Punctuation): __ /10  
   - Comment:

6. Creativity & Originality: __ /10  
   - Comment:

**Total**: __ /100  
**Converted (÷5)**: __ /20  
**Band**: Cemerlang / Kepujian / Baik / Memuaskan / TPM / TM

---

### Key Feedback (3 bullets)
- …
- …
- …

**Next-step Action:** One clear, practical suggestion for improvement.

IMPORTANT: You must respond with EXACTLY the format above. Use the exact template structure and fill in the scores and comments appropriately.`;

  async handleMarkingRequest(request: MarkingServiceRequest): Promise<MarkingServiceResponse> {
    try {
      // Validate request
      if (!request.topic || !request.essayContent) {
        return {
          success: false,
          error: 'Invalid request: topic and essayContent are required',
          statusCode: 400
        };
      }

      // Construct the essay text for marking
      const fullEssay = `\n**Topic:** ${request.topic}\n\n**Introduction:**\n${request.essayContent.introduction || '[No introduction provided]'}\n\n**Main Points:**\n${request.essayContent.mainPoints.map((point, index) => `Point ${index + 1}: ${point || '[No content provided]'}`).join('\n\n')}\n\n**Conclusion:**\n${request.essayContent.conclusion || '[No conclusion provided]'}\n    `.trim();

      const prompt = `Please mark this Year 5 Bahasa Melayu essay according to the KSSR 2022 guidelines:\n\n${fullEssay}\n\nPlease provide your marking using the exact template format specified in the system prompt.`;

      const messages: ChatRequest['messages'] = [
        { role: 'system', content: MarkingService.markingSystemPrompt },
        { role: 'user', content: prompt }
      ];

      // Always use the marking model from .env
      const selectedModel = process.env.OPENROUTER_MODEL_MARKER || process.env.OPENROUTER_MODEL_QUESTIONS;

      // Call OpenRouter client
      const response = await this.openRouterClient.complete({
        messages,
        model: selectedModel,
        temperature: request.temperature ?? 0.2,
        max_tokens: request.max_tokens ?? 1000
      });

      return {
        success: true,
        data: response
      };

    } catch (error) {
      console.error('Error in MarkingService.handleMarkingRequest:', error);
      if (error instanceof Error && error.message.includes('OpenRouter API error')) {
        const statusMatch = error.message.match(/OpenRouter API error: (\d+)/);
        const statusCode = statusMatch ? parseInt(statusMatch[1]) : 500;
        return {
          success: false,
          error: 'Failed to get response from AI service',
          statusCode
        };
      }
      return {
        success: false,
        error: 'Internal server error',
        statusCode: 500
      };
    }
  }
}
