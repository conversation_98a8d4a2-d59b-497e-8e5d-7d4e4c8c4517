{"result": [{"scriptId": "357", "url": "file:///D:/projects/GitHub/bm-essay-tutor/apps/api/src/__tests__/integration.test.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 20546, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 20546, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 676, "endOffset": 6880, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 753, "endOffset": 1091, "count": 10}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1142, "endOffset": 1580, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1210, "endOffset": 1574, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1634, "endOffset": 6431, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1713, "endOffset": 2652, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2724, "endOffset": 3036, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3106, "endOffset": 3432, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3503, "endOffset": 4052, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4123, "endOffset": 4631, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4710, "endOffset": 5527, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 5589, "endOffset": 5839, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 5923, "endOffset": 6425, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 6025, "endOffset": 6085, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 6508, "endOffset": 6876, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 6602, "endOffset": 6870, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 6778, "endOffset": 6797, "count": 1}], "isBlockCoverage": true}]}, {"scriptId": "499", "url": "file:///D:/projects/GitHub/bm-essay-tutor/apps/api/src/services/chatService.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 9030, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 9030, "count": 1}], "isBlockCoverage": true}, {"functionName": "ChatService", "ranges": [{"startOffset": 346, "endOffset": 427, "count": 11}], "isBlockCoverage": true}, {"functionName": "<static_initializer>", "ranges": [{"startOffset": 430, "endOffset": 1384, "count": 1}], "isBlockCoverage": true}, {"functionName": "handleStudentPrompt", "ranges": [{"startOffset": 1388, "endOffset": 2810, "count": 5}, {"startOffset": 1533, "endOffset": 1706, "count": 0}, {"startOffset": 1922, "endOffset": 1951, "count": 0}, {"startOffset": 2142, "endOffset": 2219, "count": 2}, {"startOffset": 2219, "endOffset": 2806, "count": 3}, {"startOffset": 2392, "endOffset": 2688, "count": 1}, {"startOffset": 2540, "endOffset": 2545, "count": 0}, {"startOffset": 2688, "endOffset": 2806, "count": 2}], "isBlockCoverage": true}, {"functionName": "get", "ranges": [{"startOffset": 2912, "endOffset": 2939, "count": 11}], "isBlockCoverage": true}]}, {"scriptId": "501", "url": "file:///D:/projects/GitHub/bm-essay-tutor/apps/api/src/index.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 26395, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 26395, "count": 1}, {"startOffset": 8521, "endOffset": 8528, "count": 0}], "isBlockCoverage": true}, {"functionName": "buildServer", "ranges": [{"startOffset": 1352, "endOffset": 8333, "count": 12}, {"startOffset": 1481, "endOffset": 1899, "count": 2}, {"startOffset": 1551, "endOffset": 1899, "count": 1}, {"startOffset": 1899, "endOffset": 2038, "count": 11}, {"startOffset": 2038, "endOffset": 2091, "count": 0}, {"startOffset": 2091, "endOffset": 2232, "count": 11}, {"startOffset": 2232, "endOffset": 2263, "count": 0}, {"startOffset": 2264, "endOffset": 2316, "count": 11}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2677, "endOffset": 2922, "count": 2}, {"startOffset": 2894, "endOffset": 2910, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2993, "endOffset": 4617, "count": 5}, {"startOffset": 3338, "endOffset": 3350, "count": 4}, {"startOffset": 3552, "endOffset": 3566, "count": 2}, {"startOffset": 3568, "endOffset": 3773, "count": 2}, {"startOffset": 3773, "endOffset": 4072, "count": 3}, {"startOffset": 3989, "endOffset": 3995, "count": 0}, {"startOffset": 4034, "endOffset": 4052, "count": 0}, {"startOffset": 4079, "endOffset": 4613, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4840, "endOffset": 8314, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 8433, "endOffset": 8460, "count": 11}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 8547, "endOffset": 8761, "count": 1}], "isBlockCoverage": true}]}, {"scriptId": "584", "url": "file:///D:/projects/GitHub/bm-essay-tutor/apps/api/src/services/markingService.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 17553, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 17553, "count": 1}], "isBlockCoverage": true}, {"functionName": "MarkingService", "ranges": [{"startOffset": 349, "endOffset": 430, "count": 11}], "isBlockCoverage": true}, {"functionName": "<static_initializer>", "ranges": [{"startOffset": 433, "endOffset": 3700, "count": 1}], "isBlockCoverage": true}, {"functionName": "handleMarkingRequest", "ranges": [{"startOffset": 3704, "endOffset": 5707, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 5812, "endOffset": 5842, "count": 11}], "isBlockCoverage": true}]}, {"scriptId": "585", "url": "file:///D:/projects/GitHub/bm-essay-tutor/apps/api/src/clients/openRouter.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 4057, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 4057, "count": 1}], "isBlockCoverage": true}, {"functionName": "OpenRouterClientImpl", "ranges": [{"startOffset": 216, "endOffset": 267, "count": 12}], "isBlockCoverage": true}, {"functionName": "complete", "ranges": [{"startOffset": 270, "endOffset": 1027, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 1138, "endOffset": 1174, "count": 12}], "isBlockCoverage": true}]}, {"scriptId": "586", "url": "file:///D:/projects/GitHub/bm-essay-tutor/apps/api/src/middleware/logger.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 7491, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 7491, "count": 1}, {"startOffset": 525, "endOffset": 668, "count": 0}], "isBlockCoverage": true}, {"functionName": "level", "ranges": [{"startOffset": 754, "endOffset": 803, "count": 6}], "isBlockCoverage": true}, {"functionName": "get", "ranges": [{"startOffset": 1075, "endOffset": 1097, "count": 20}], "isBlockCoverage": true}, {"functionName": "genReqId", "ranges": [{"startOffset": 1208, "endOffset": 1344, "count": 0}], "isBlockCoverage": false}, {"functionName": "req", "ranges": [{"startOffset": 1417, "endOffset": 1740, "count": 0}], "isBlockCoverage": false}, {"functionName": "res", "ranges": [{"startOffset": 1751, "endOffset": 2083, "count": 0}], "isBlockCoverage": false}, {"functionName": "customLogLevel", "ranges": [{"startOffset": 2150, "endOffset": 2431, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 2534, "endOffset": 2560, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "635", "url": "file:///D:/projects/GitHub/bm-essay-tutor/apps/api/src/middleware/security.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 12437, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 12437, "count": 1}], "isBlockCoverage": true}, {"functionName": "keyGenerator", "ranges": [{"startOffset": 1002, "endOffset": 1194, "count": 7}, {"startOffset": 1085, "endOffset": 1117, "count": 0}, {"startOffset": 1119, "endOffset": 1171, "count": 0}], "isBlockCoverage": true}, {"functionName": "handler", "ranges": [{"startOffset": 1251, "endOffset": 1721, "count": 0}], "isBlockCoverage": false}, {"functionName": "skip", "ranges": [{"startOffset": 1773, "endOffset": 1822, "count": 9}], "isBlockCoverage": true}, {"functionName": "get", "ranges": [{"startOffset": 1926, "endOffset": 1953, "count": 11}], "isBlockCoverage": true}, {"functionName": "get", "ranges": [{"startOffset": 3323, "endOffset": 3354, "count": 11}], "isBlockCoverage": true}, {"functionName": "enforceHTTPS", "ranges": [{"startOffset": 3358, "endOffset": 4081, "count": 9}, {"startOffset": 3467, "endOffset": 3545, "count": 0}, {"startOffset": 3546, "endOffset": 3588, "count": 0}, {"startOffset": 3607, "endOffset": 4080, "count": 0}], "isBlockCoverage": true}, {"functionName": "get", "ranges": [{"startOffset": 4182, "endOffset": 4210, "count": 11}], "isBlockCoverage": true}]}, {"scriptId": "640", "url": "file:///D:/projects/GitHub/bm-essay-tutor/apps/api/src/middleware/validation.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 10312, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 10312, "count": 1}], "isBlockCoverage": true}, {"functionName": "validateRequest", "ranges": [{"startOffset": 1086, "endOffset": 2311, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1130, "endOffset": 2308, "count": 7}, {"startOffset": 1460, "endOffset": 2304, "count": 2}, {"startOffset": 1975, "endOffset": 2159, "count": 0}, {"startOffset": 2160, "endOffset": 2177, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1858, "endOffset": 1954, "count": 2}], "isBlockCoverage": true}, {"functionName": "get", "ranges": [{"startOffset": 2415, "endOffset": 2446, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 2621, "endOffset": 2656, "count": 11}], "isBlockCoverage": true}, {"functionName": "validateRequestSize", "ranges": [{"startOffset": 2660, "endOffset": 3226, "count": 11}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2726, "endOffset": 3223, "count": 9}, {"startOffset": 2813, "endOffset": 2819, "count": 2}, {"startOffset": 2860, "endOffset": 3207, "count": 0}], "isBlockCoverage": true}, {"functionName": "get", "ranges": [{"startOffset": 3334, "endOffset": 3369, "count": 11}], "isBlockCoverage": true}]}]}