import { defineConfig, loadEnv } from 'vite'
import react from '@vitejs/plugin-react'

// https://vitejs.dev/config/
export default defineConfig(({ mode }) => {
  const isDev = mode === 'development';
  // Load env file based on `mode` in the current working directory.
  // Set the third parameter to '' to load all env regardless of the `VITE_` prefix.
  const env = loadEnv(mode, process.cwd(), '')

  return {
    plugins: [react()],
    // Configuration for development mode
    server: {
      hmr: true,
      allowedHosts: [
        'f113-180-74-174-157.ngrok-free.app'
      ],
      // Enable more detailed logging in development
      ...(isDev && {
        clearScreen: false,
        logLevel: 'info',
      }),
      // Proxy API requests to the backend
      proxy: {
        '/api': 'http://localhost:3001'
      }
    },
    // Enable debug logging in development
    define: {
      __DEV__: isDev,
      // Expose model configuration to the client (no API key)
      'import.meta.env.VITE_OPENROUTER_MODEL_TRANSLATION': JSON.stringify(env.OPENROUTER_MODEL_TRANSLATION || 'anthropic/claude-3-haiku'),
      'import.meta.env.VITE_OPENROUTER_MODEL_IDEATION': JSON.stringify(env.OPENROUTER_MODEL_IDEATION || 'anthropic/claude-3-haiku'),
      'import.meta.env.VITE_OPENROUTER_MODEL_QUESTIONS': JSON.stringify(env.OPENROUTER_MODEL_QUESTIONS || 'anthropic/claude-3-haiku'),
      'import.meta.env.VITE_OPENROUTER_MODEL_MARKER': JSON.stringify(env.OPENROUTER_MODEL_MARKER || 'anthropic/claude-3-sonnet'),
      'import.meta.env.VITE_API_BASE_URL': JSON.stringify(env.VITE_API_BASE_URL || 'http://localhost:4000'),
    }
  }
})
