import axios from 'axios';
import { logger } from './debugLogger';

interface ChatResponse {
  choices: {
    message: {
      content: string;
    };
  }[];
}

interface ChatRequest {
  messages: {
    role: 'system' | 'user' | 'assistant';
    content: string;
  }[];
  model?: string;
  temperature?: number;
  max_tokens?: number;
}

export const getTranslationResponse = async (
  prompt: string,
  previousMessages: { role: 'user' | 'assistant'; content: string }[] = []
): Promise<string> => {
  logger.info('Translation', '🚀 Starting new translation request', { prompt });

  if (!prompt) {
    logger.warn('Translation', '⚠️ Empty prompt received');
    return '';
  }

  try {
    // Only send user/assistant messages and the user prompt
    const messages: ChatRequest['messages'] = [
      ...previousMessages,
      { role: 'user', content: prompt }
    ];

    logger.info('Translation', '📝 Using conversation history', {
      messageCount: messages.length,
      lastUserMessage: prompt,
      fullHistory: messages
    });

    // Call our local API instead of OpenRouter directly
    const response = await axios.post<ChatResponse>(
      '/api/chat',
      { messages },
      {
        headers: {
          'Content-Type': 'application/json'
        }
      }
    );

    logger.info('Translation', '✅ Received API response', {
      status: response.status,
      hasChoices: response.data.choices?.length > 0,
      headers: response.headers
    });

    const content = response.data.choices[0].message.content;
    logger.info('Translation', '📤 Processing response content', {
      contentLength: content.length,
      preview: content.substring(0, 50) + '...',
      fullResponse: response.data
    });

    return content;
  } catch (error) {    if (axios.isAxiosError(error)) {
      logger.error('Translation', '🔴 Network or API error', {
        status: error.response?.status,
        statusText: error.response?.statusText,
        data: error.response?.data,
        message: error.message,
        config: {
          url: error.config?.url,
          method: error.config?.method,
          headers: error.config?.headers
        }
      });
    } else {
      logger.error('Translation', '🔴 Unexpected error', {
        error: error instanceof Error ? error.message : error
      });
    }
    return 'Sorry, I encountered an error. Please try again later.';
  }
};
