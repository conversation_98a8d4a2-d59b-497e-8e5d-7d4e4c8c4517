import axios from 'axios';
import { logger } from './debugLogger';

export interface EssayMarkingResult {
  ideasContent: { score: number; comment: string };
  structureOrganization: { score: number; comment: string };
  languageUse: { score: number; comment: string };
  vocabulary: { score: number; comment: string };
  mechanics: { score: number; comment: string };
  creativity: { score: number; comment: string };
  total: number;
  convertedScore: number;
  band: string;
  keyFeedback: string[];
  nextStepAction: string;
}

// Remove systemPrompt and getEssayMarking implementation, replace with API call to backend

export const getEssayMarking = async (
  topic: string,
  essayContent: {
    introduction: string;
    mainPoints: string[];
    conclusion: string;
  }
): Promise<EssayMarkingResult | null> => {
  logger.info('Marker', '🚀 Starting essay marking request', { 
    topic,
    essayLength: {
      introduction: essayContent.introduction.length,
      mainPoints: essayContent.mainPoints.map(p => p.length),
      conclusion: essayContent.conclusion.length
    }
  });

  try {
    // Call our local API which now handles all prompt logic and parsing
    const response = await axios.post<{ result: EssayMarkingResult }>(
      '/api/marking',
      {
        topic,
        essayContent
      },
      {
        headers: {
          'Content-Type': 'application/json'
        }
      }
    );

    logger.info('Marker', '✅ Received marking response', {
      status: response.status,
      hasResult: !!response.data.result
    });

    return response.data.result;
  } catch (error) {
    if (axios.isAxiosError(error)) {
      logger.error('Marker', '🔴 Network or API error', {
        status: error.response?.status,
        statusText: error.response?.statusText,
        data: error.response?.data,
        message: error.message
      });
    } else {
      logger.error('Marker', '🔴 Unexpected error', {
        error: error instanceof Error ? error.message : error
      });
    }
    return null;
  }
};

// Remove parseMarkingResponse, now handled server-side
