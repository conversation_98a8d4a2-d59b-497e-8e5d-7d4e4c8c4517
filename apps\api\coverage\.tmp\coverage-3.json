{"result": [{"scriptId": "357", "url": "file:///D:/projects/GitHub/bm-essay-tutor/apps/api/src/services/__tests__/chatService.test.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 16670, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 16670, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 564, "endOffset": 5632, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 626, "endOffset": 762, "count": 7}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 821, "endOffset": 5628, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 912, "endOffset": 1796, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1884, "endOffset": 2663, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2743, "endOffset": 3226, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3302, "endOffset": 3699, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3770, "endOffset": 4352, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4416, "endOffset": 4957, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 5048, "endOffset": 5622, "count": 1}], "isBlockCoverage": true}]}, {"scriptId": "359", "url": "file:///D:/projects/GitHub/bm-essay-tutor/apps/api/src/services/chatService.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 9030, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 9030, "count": 1}], "isBlockCoverage": true}, {"functionName": "ChatService", "ranges": [{"startOffset": 346, "endOffset": 427, "count": 7}], "isBlockCoverage": true}, {"functionName": "<static_initializer>", "ranges": [{"startOffset": 430, "endOffset": 1384, "count": 1}], "isBlockCoverage": true}, {"functionName": "handleStudentPrompt", "ranges": [{"startOffset": 1388, "endOffset": 2810, "count": 7}, {"startOffset": 1463, "endOffset": 1498, "count": 6}, {"startOffset": 1499, "endOffset": 1531, "count": 6}, {"startOffset": 1533, "endOffset": 1706, "count": 2}, {"startOffset": 1706, "endOffset": 1922, "count": 5}, {"startOffset": 1922, "endOffset": 1951, "count": 0}, {"startOffset": 2142, "endOffset": 2219, "count": 2}, {"startOffset": 2219, "endOffset": 2806, "count": 3}, {"startOffset": 2392, "endOffset": 2688, "count": 2}, {"startOffset": 2513, "endOffset": 2539, "count": 1}, {"startOffset": 2540, "endOffset": 2545, "count": 1}, {"startOffset": 2688, "endOffset": 2806, "count": 1}], "isBlockCoverage": true}, {"functionName": "get", "ranges": [{"startOffset": 2912, "endOffset": 2939, "count": 7}], "isBlockCoverage": true}]}]}