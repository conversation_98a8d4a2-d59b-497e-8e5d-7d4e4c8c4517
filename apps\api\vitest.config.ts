import { defineConfig } from 'vitest/config';

export default defineConfig({
  test: {
    globals: true,
    environment: 'node',
    coverage: {
      provider: 'v8',
      reporter: ['text', 'json', 'html'],
      exclude: [
        'node_modules/**',
        'dist/**',
        '**/*.d.ts',
        '**/*.config.*',
        '**/test-api.js',
        '**/middleware/logger.ts',
        '**/.eslintrc.cjs'
      ],
      thresholds: {
        lines: 85,
        functions: 80,
        branches: 80,
        statements: 85
      }
    }
  }
});
