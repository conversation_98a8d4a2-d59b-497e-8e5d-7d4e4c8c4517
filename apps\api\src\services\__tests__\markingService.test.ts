import { MarkingService, MarkingServiceRequest } from '../markingService.js'
import { OpenRouterClient } from '../../clients/openRouter.js'

describe('MarkingService', () => {
  let openRouterClient: OpenRouterClient
  let service: MarkingService

  beforeEach(() => {
    openRouterClient = {
      complete: jest.fn()
    } as any
    service = new MarkingService(openRouterClient)
  })

  it('should return a parsed EssayMarkingResult for a valid essay', async () => {
    const mockEssay = {
      topic: 'Kepentingan Menjaga Kebersihan',
      essayContent: {
        introduction: '<PERSON><PERSON><PERSON><PERSON> sangat penting dalam kehidupan.',
        mainPoints: ['<PERSON><PERSON><PERSON><PERSON> mencegah penyakit.', 'Lingkungan bersih nyaman didiami.'],
        conclusion: 'Kita harus sentiasa menjaga kebersihan.'
      }
    }
    const mockAIResponse = {
      choices: [{ message: { content: `📝 **Essay Marking Report**\n\n1. Ideas & Content: 20 /25  \n   - Comment: Good ideas.\n2. Structure & Organization: 15 /20  \n   - Comment: Well structured.\n3. Language Use: 18 /20  \n   - Comment: Good grammar.\n4. Vocabulary: 12 /15  \n   - Comment: Adequate vocabulary.\n5. Mechanics (Spelling & Punctuation): 8 /10  \n   - Comment: Minor errors.\n6. Creativity & Originality: 7 /10  \n   - Comment: Some originality.\n**Total**: 80 /100  \n**Converted (÷5)**: 16 /20  \n**Band**: Kepujian\n---\n### Key Feedback (3 bullets)\n- Good structure\n- Clear ideas\n- Needs more creativity\n**Next-step Action:** Practice more creative writing.` } }] }
    (openRouterClient.complete as jest.Mock).mockResolvedValue(mockAIResponse)

    const result = await service.handleMarkingRequest(mockEssay as MarkingServiceRequest)
    expect(result.success).toBe(true)
    expect(result.data).toBeDefined()
    // Optionally, check for parsed fields if you add parsing logic
  })

  it('should return error for missing topic', async () => {
    const result = await service.handleMarkingRequest({ topic: '', essayContent: { introduction: '', mainPoints: [], conclusion: '' } })
    expect(result.success).toBe(false)
    expect(result.statusCode).toBe(400)
  })
})
