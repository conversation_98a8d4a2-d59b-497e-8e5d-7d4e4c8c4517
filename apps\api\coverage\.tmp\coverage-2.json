{"result": [{"scriptId": "357", "url": "file:///D:/projects/GitHub/bm-essay-tutor/apps/api/src/services/__tests__/markingService.test.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 6770, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 6770, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 343, "endOffset": 2160, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 403, "endOffset": 546, "count": 2}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 617, "endOffset": 1871, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 1920, "endOffset": 2156, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "358", "url": "file:///D:/projects/GitHub/bm-essay-tutor/apps/api/src/services/markingService.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 17553, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 17553, "count": 1}], "isBlockCoverage": true}, {"functionName": "MarkingService", "ranges": [{"startOffset": 349, "endOffset": 430, "count": 0}], "isBlockCoverage": false}, {"functionName": "<static_initializer>", "ranges": [{"startOffset": 433, "endOffset": 3700, "count": 1}], "isBlockCoverage": true}, {"functionName": "handleMarkingRequest", "ranges": [{"startOffset": 3704, "endOffset": 5707, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 5812, "endOffset": 5842, "count": 0}], "isBlockCoverage": false}]}]}