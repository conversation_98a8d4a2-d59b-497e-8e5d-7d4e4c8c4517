# BM Essay Tutor Web App - Environment Variables

# API Configuration
VITE_API_BASE_URL=http://localhost:4000

# OpenRouter API Models
# Different models are used for different tasks to optimize performance
# Available models include:
# - anthropic/claude-3-opus (Most capable, highest cost)
# - anthropic/claude-3-sonnet (Balanced performance and cost)
# - anthropic/claude-3-haiku (Fastest, lowest cost)
# - openai/gpt-4 (Strong creative and reasoning capabilities)
# - openai/gpt-3.5-turbo (Fast and cost-effective)
# - google/gemini-pro (Good all-rounder)
# - google/gemini-1.5-pro (Enhanced capabilities)

# Translation tasks - Fast model for simple translations
VITE_OPENROUTER_MODEL_TRANSLATION=anthropic/claude-3-haiku

# Ideation tasks - Creative model for generating ideas
VITE_OPENROUTER_MODEL_IDEATION=anthropic/claude-3-haiku

# Question answering and feedback - Balanced model
VITE_OPENROUTER_MODEL_QUESTIONS=anthropic/claude-3-haiku

# Essay marking - More capable model for detailed analysis
VITE_OPENROUTER_MODEL_MARKER=anthropic/claude-3-sonnet
