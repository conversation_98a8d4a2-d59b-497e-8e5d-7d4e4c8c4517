import express from 'express';
import cors from 'cors';
import dotenv from 'dotenv';
import { MarkingService } from './services/markingService.js';
import { OpenRouterClientImpl } from './clients/openRouter.js';
import { ChatService } from './services/chatService.js';
import type { ChatRequest } from './clients/openRouter.js';
import { logger, httpLogger } from './middleware/logger.js';
import { rateLimiter, securityHeaders, enforceHTTPS } from './middleware/security.js';
import { validateChatRequest, validateRequestSize } from './middleware/validation.js';

// Load environment variables
dotenv.config();

export function buildServer(chatService?: ChatService) {
  const app = express();

  // Initialize chat service if not provided (for production use)
  let service = chatService;
  if (!service) {
    const apiKey = process.env.OPENROUTER_API_KEY;
    if (!apiKey) {
      logger.error('OPENROUTER_API_KEY not found in environment variables');
      throw new Error('OPENROUTER_API_KEY not found in environment variables');
    }
    const openRouterClient = new OpenRouterClientImpl(apiKey);
    service = new ChatService(openRouterClient);
  }

  // Security middleware (order matters!)
  app.use(enforceHTTPS); // HTTPS enforcement first
  app.use(securityHeaders); // Security headers

  // Only use HTTP logger in non-test environments
  if (process.env.NODE_ENV !== 'test') {
    app.use(httpLogger); // Request logging
  }

  app.use(rateLimiter); // Rate limiting

  // CORS configuration with strict allow-list
  app.use(cors({
    origin: process.env.NODE_ENV === 'production'
      ? ['https://bm-essay-tutor.my'] // Production domain
      : ['http://localhost:5173', 'http://localhost:3000'], // Development ports
    credentials: true,
    methods: ['GET', 'POST'],
    allowedHeaders: ['Content-Type', 'Authorization'],
    optionsSuccessStatus: 200 // For legacy browser support
  }));

  // Body parsing with size limits
  app.use(validateRequestSize(10 * 1024)); // 10KB limit
  app.use(express.json({ limit: '10kb' }));

  // Health check endpoint (no rate limiting applied)
  app.get('/health', (req, res) => {
    logger.info('Health check requested');
    res.json({
      status: 'ok',
      timestamp: new Date().toISOString(),
      environment: process.env.NODE_ENV || 'development'
    });
  });

  // Chat proxy endpoint with validation
  app.post('/api/chat', validateChatRequest, async (req, res) => {
    const requestId = req.headers['x-request-id'] || Math.random().toString(36).substring(2, 15);

    try {
      const { messages, model, temperature, max_tokens }: ChatRequest = req.body;

      logger.info('Chat request received', {
        requestId,
        messageCount: messages.length,
        model: model || 'default',
        clientIP: req.ip
      });

      const result = await service.handleStudentPrompt({
        messages,
        model,
        temperature,
        max_tokens
      });

      if (result.success && result.data) {
        logger.info('Chat request successful', {
          requestId,
          responseLength: JSON.stringify(result.data).length
        });
        res.json(result.data);
      } else {
        logger.warn('Chat request failed', {
          requestId,
          error: result.error,
          statusCode: result.statusCode
        });
        res.status(result.statusCode || 500).json({
          error: result.error || 'Unknown error'
        });
      }

    } catch (error) {
      logger.error('Unexpected error in chat endpoint', {
        requestId,
        error: error instanceof Error ? error.message : 'Unknown error',
        stack: process.env.NODE_ENV === 'development' ?
               (error instanceof Error ? error.stack : undefined) : undefined
      });

      // Never leak sensitive information in production
      res.status(500).json({
        error: 'Internal server error',
        ...(process.env.NODE_ENV === 'development' && {
          message: error instanceof Error ? error.message : 'Unknown error'
        })
      });
    }
  });

  // Add MarkingService initialization
  const openRouterClient = new OpenRouterClientImpl(process.env.OPENROUTER_API_KEY!);
  const markingService = new MarkingService(openRouterClient);

  // Essay marking endpoint
  app.post('/api/marking', async (req, res) => {
    const { topic, essayContent } = req.body;
    try {
      const result = await markingService.handleMarkingRequest({ topic, essayContent });
      if (result.success && result.data) {
        const content = result.data.choices?.[0]?.message?.content || '';
        // Parse the AI response into EssayMarkingResult structure
        const ideasMatch = content.match(/1\.\s*Ideas & Content:\s*(\d+)\s*\/25\s*-\s*Comment:\s*([^\n]+(?:\n(?!\d\.|###|\*\*)[^\n]*)*)/i);
        const structureMatch = content.match(/2\.\s*Structure & Organization:\s*(\d+)\s*\/20\s*-\s*Comment:\s*([^\n]+(?:\n(?!\d\.|###|\*\*)[^\n]*)*)/i);
        const languageMatch = content.match(/3\.\s*Language Use:\s*(\d+)\s*\/20\s*-\s*Comment:\s*([^\n]+(?:\n(?!\d\.|###|\*\*)[^\n]*)*)/i);
        const vocabularyMatch = content.match(/4\.\s*Vocabulary:\s*(\d+)\s*\/15\s*-\s*Comment:\s*([^\n]+(?:\n(?!\d\.|###|\*\*)[^\n]*)*)/i);
        const mechanicsMatch = content.match(/5\.\s*Mechanics.*?:\s*(\d+)\s*\/10\s*-\s*Comment:\s*([^\n]+(?:\n(?!\d\.|###|\*\*)[^\n]*)*)/i);
        const creativityMatch = content.match(/6\.\s*Creativity & Originality:\s*(\d+)\s*\/10\s*-\s*Comment:\s*([^\n]+(?:\n(?!\d\.|###|\*\*)[^\n]*)*)/i);
        const totalMatch = content.match(/\*\*Total\*\*:\s*(\d+)\s*\/100/i);
        const convertedMatch = content.match(/\*\*Converted.*?\*\*:\s*(\d+)\s*\/20/i);
        const bandMatch = content.match(/\*\*Band\*\*:\s*([^\n]+)/i);
        const feedbackSection = content.match(/### Key Feedback.*?\n((?:- [^\n]+\n?)+)/s);
        const keyFeedback = feedbackSection ?
          feedbackSection[1].split('\n').filter(line => line.trim().startsWith('-')).map(line => line.replace(/^-\s*/, '').trim()) :
          [];
        const nextStepMatch = content.match(/\*\*Next-step Action:\*\*\s*([^\n]+)/i);
        if (!ideasMatch || !structureMatch || !languageMatch || !vocabularyMatch || !mechanicsMatch || !creativityMatch || !totalMatch || !convertedMatch || !bandMatch) {
          return res.status(500).json({ error: 'Could not parse marking response', raw: content });
        }
        const essayMarkingResult = {
          ideasContent: {
            score: parseInt(ideasMatch[1]),
            comment: ideasMatch[2].trim()
          },
          structureOrganization: {
            score: parseInt(structureMatch[1]),
            comment: structureMatch[2].trim()
          },
          languageUse: {
            score: parseInt(languageMatch[1]),
            comment: languageMatch[2].trim()
          },
          vocabulary: {
            score: parseInt(vocabularyMatch[1]),
            comment: vocabularyMatch[2].trim()
          },
          mechanics: {
            score: parseInt(mechanicsMatch[1]),
            comment: mechanicsMatch[2].trim()
          },
          creativity: {
            score: parseInt(creativityMatch[1]),
            comment: creativityMatch[2].trim()
          },
          total: parseInt(totalMatch[1]),
          convertedScore: parseInt(convertedMatch[1]),
          band: bandMatch[1].trim(),
          keyFeedback: keyFeedback.slice(0, 3),
          nextStepAction: nextStepMatch ? nextStepMatch[1].trim() : 'Continue practicing essay writing.'
        };
        return res.json({ result: essayMarkingResult });
      } else {
        return res.status(result.statusCode || 500).json({ error: result.error || 'Unknown error' });
      }
    } catch (error) {
      return res.status(500).json({ error: error instanceof Error ? error.message : 'Internal server error' });
    }
  });

  return app;
}

// Start server if this file is run directly
const app = buildServer();
const port = process.env.PORT || 3001;

app.listen(port, () => {
  console.log(`🚀 API server running on http://localhost:${port}`);
  console.log(`📋 Health check: http://localhost:${port}/health`);
  console.log(`💬 Chat endpoint: http://localhost:${port}/api/chat`);
});
