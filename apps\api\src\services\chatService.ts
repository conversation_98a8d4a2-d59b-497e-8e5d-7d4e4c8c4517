import { OpenRouterClient, ChatRequest, ChatResponse } from '../clients/openRouter.js';
import dotenv from 'dotenv';

dotenv.config();

export interface ChatServiceRequest {
  messages: ChatRequest['messages'];
  model?: string;
  temperature?: number;
  max_tokens?: number;
}

export interface ChatServiceResponse {
  success: boolean;
  data?: ChatResponse;
  error?: string;
  statusCode?: number;
}

export class ChatService {
  constructor(private openRouterClient: OpenRouterClient) {}

  private static readonly translationSystemPrompt = `You are a Year 5 Malay tutor. The student will ask for an English→Malay translation.
1. NEVER reveal the full answer in a hint. You can only reveal the full answer after the student has given up or after 3 incorrect attempts.
2. Provide percentage‑based reveals of the target word over three attempts:
   - **First hint (50% reveal):** display half the letters in their correct positions.
   - **Second hint (75% reveal):** display three‑quarters of the letters.
   - **Third hint (90% reveal):** display ninety percent of the letters.
2. After the third incorrect attempt, reveal the complete word.

Example (word: "memberi", 7 letters):
- AI: "Here's a hint, it starts with mem and has 7 letters"
- Student: "membeli?"
- AI: "Not correct. Here's a second hint: It start with membe
- Student: "member?"
- AI: "Still not correct. Final hint!: it start with member"
- Student: "I give up."
- AI: "The word is 'memberi'."`;

  async handleStudentPrompt(request: ChatServiceRequest): Promise<ChatServiceResponse> {
    try {
      // Validate request
      if (!request.messages || !Array.isArray(request.messages) || request.messages.length === 0) {
        return {
          success: false,
          error: 'Invalid request: messages array is required and cannot be empty',
          statusCode: 400
        };
      }

      // Prepend system prompt for translation activity
      const messagesWithSystem: ChatRequest['messages'] = [
        { role: 'system', content: ChatService.translationSystemPrompt },
        ...request.messages
      ];

      // Always use the translation model from .env
      const selectedModel = process.env.OPENROUTER_MODEL_TRANSLATION || 'anthropic/claude-3-haiku';

      // Call OpenRouter client
      const response = await this.openRouterClient.complete({
        messages: messagesWithSystem,
        model: selectedModel,
        temperature: 0.7,
        max_tokens: 150
      });

      return {
        success: true,
        data: response
      };

    } catch (error) {
      console.error('Error in ChatService.handleStudentPrompt:', error);
      
      // Handle OpenRouter API errors specifically
      if (error instanceof Error && error.message.includes('OpenRouter API error')) {
        const statusMatch = error.message.match(/OpenRouter API error: (\d+)/);
        const statusCode = statusMatch ? parseInt(statusMatch[1]) : 500;
        
        return {
          success: false,
          error: 'Failed to get response from AI service',
          statusCode
        };
      }

      return {
        success: false,
        error: 'Internal server error',
        statusCode: 500
      };
    }
  }
}
