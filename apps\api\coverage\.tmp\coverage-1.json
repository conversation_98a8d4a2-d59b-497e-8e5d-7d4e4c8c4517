{"result": [{"scriptId": "357", "url": "file:///D:/projects/GitHub/bm-essay-tutor/apps/api/src/clients/__tests__/openRouter.test.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 15263, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 15263, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 579, "endOffset": 5017, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 669, "endOffset": 795, "count": 5}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 843, "endOffset": 5013, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 939, "endOffset": 2160, "count": 1}], "isBlockCoverage": true}, {"functionName": "json", "ranges": [{"startOffset": 1173, "endOffset": 1208, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2238, "endOffset": 3199, "count": 1}], "isBlockCoverage": true}, {"functionName": "json", "ranges": [{"startOffset": 2461, "endOffset": 2496, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3289, "endOffset": 4181, "count": 1}], "isBlockCoverage": true}, {"functionName": "json", "ranges": [{"startOffset": 3513, "endOffset": 3548, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4256, "endOffset": 4663, "count": 1}], "isBlockCoverage": true}, {"functionName": "text", "ranges": [{"startOffset": 4411, "endOffset": 4447, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4737, "endOffset": 5007, "count": 1}], "isBlockCoverage": true}]}, {"scriptId": "359", "url": "file:///D:/projects/GitHub/bm-essay-tutor/apps/api/src/clients/openRouter.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 4057, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 4057, "count": 1}], "isBlockCoverage": true}, {"functionName": "OpenRouterClientImpl", "ranges": [{"startOffset": 216, "endOffset": 267, "count": 5}], "isBlockCoverage": true}, {"functionName": "complete", "ranges": [{"startOffset": 270, "endOffset": 1027, "count": 5}, {"startOffset": 832, "endOffset": 855, "count": 4}, {"startOffset": 855, "endOffset": 989, "count": 1}, {"startOffset": 989, "endOffset": 1026, "count": 3}], "isBlockCoverage": true}, {"functionName": "get", "ranges": [{"startOffset": 1138, "endOffset": 1174, "count": 5}], "isBlockCoverage": true}]}]}